import { CustomerContract, JobDetail, WorkShift } from '../models';
import { calculateWorkingDates } from './workingDaysUtils';

/**
 * Calculate the total contract amount based on work shifts and contract dates
 * @param contract The customer contract with job details and work shifts
 * @returns Object containing total amount and calculation breakdown
 */
export const calculateContractAmount = (contract: Partial<CustomerContract>): {
  totalAmount: number;
  breakdown: ContractCalculationBreakdown[];
  summary: ContractCalculationSummary;
} => {
  if (!contract.jobDetails || contract.jobDetails.length === 0) {
    return {
      totalAmount: 0,
      breakdown: [],
      summary: {
        totalWorkShifts: 0,
        totalWorkers: 0,
        totalWorkingDays: 0,
        contractDuration: 0
      }
    };
  }

  const breakdown: ContractCalculationBreakdown[] = [];
  let totalAmount = 0;
  let totalWorkShifts = 0;
  let totalWorkers = 0;
  let totalWorkingDays = 0;

  // Calculate contract duration in days
  const contractDuration = contract.startingDate && contract.endingDate 
    ? Math.ceil((new Date(contract.endingDate).getTime() - new Date(contract.startingDate).getTime()) / (1000 * 60 * 60 * 24)) + 1
    : 0;

  contract.jobDetails.forEach((jobDetail, jobIndex) => {
    if (!jobDetail.workShifts || jobDetail.workShifts.length === 0) return;

    const jobBreakdown: ContractCalculationBreakdown = {
      jobDetailIndex: jobIndex,
      jobCategoryName: jobDetail.jobCategoryName || `Công việc ${jobIndex + 1}`,
      workShifts: [],
      jobTotal: 0
    };

    jobDetail.workShifts.forEach((workShift, shiftIndex) => {
      if (!workShift.salary || !workShift.numberOfWorkers || !workShift.workingDays) return;

      // Use job detail dates if available, otherwise use contract dates
      const startDate = jobDetail.startDate || contract.startingDate;
      const endDate = jobDetail.endDate || contract.endingDate;

      if (!startDate || !endDate) return;

      // Calculate actual working dates for this shift
      const workingDates = calculateWorkingDates(startDate, endDate, workShift.workingDays);
      const workingDaysCount = workingDates.length;

      // Calculate amount for this shift
      const shiftAmount = workShift.salary * workShift.numberOfWorkers * workingDaysCount;

      const shiftBreakdown: WorkShiftCalculationBreakdown = {
        shiftIndex,
        startTime: workShift.startTime,
        endTime: workShift.endTime,
        salary: workShift.salary,
        numberOfWorkers: workShift.numberOfWorkers,
        workingDaysCount,
        workingDates,
        shiftAmount
      };

      jobBreakdown.workShifts.push(shiftBreakdown);
      jobBreakdown.jobTotal += shiftAmount;
      totalAmount += shiftAmount;
      totalWorkShifts++;
      totalWorkers += workShift.numberOfWorkers;
      totalWorkingDays += workingDaysCount;
    });

    if (jobBreakdown.workShifts.length > 0) {
      breakdown.push(jobBreakdown);
    }
  });

  return {
    totalAmount,
    breakdown,
    summary: {
      totalWorkShifts,
      totalWorkers,
      totalWorkingDays,
      contractDuration
    }
  };
};

/**
 * Interface for contract calculation breakdown
 */
export interface ContractCalculationBreakdown {
  jobDetailIndex: number;
  jobCategoryName: string;
  workShifts: WorkShiftCalculationBreakdown[];
  jobTotal: number;
}

/**
 * Interface for work shift calculation breakdown
 */
export interface WorkShiftCalculationBreakdown {
  shiftIndex: number;
  startTime: string;
  endTime: string;
  salary: number;
  numberOfWorkers: number;
  workingDaysCount: number;
  workingDates: string[];
  shiftAmount: number;
}

/**
 * Interface for contract calculation summary
 */
export interface ContractCalculationSummary {
  totalWorkShifts: number;
  totalWorkers: number;
  totalWorkingDays: number;
  contractDuration: number;
}

/**
 * Format calculation breakdown for display
 * @param breakdown The calculation breakdown
 * @returns Formatted string for display
 */
export const formatCalculationBreakdown = (breakdown: ContractCalculationBreakdown[]): string => {
  if (breakdown.length === 0) return 'Chưa có thông tin tính toán';

  let result = 'Chi tiết tính toán:\n\n';
  
  breakdown.forEach((job, index) => {
    result += `${job.jobCategoryName}:\n`;
    
    job.workShifts.forEach((shift, shiftIndex) => {
      result += `  Ca ${shiftIndex + 1} (${shift.startTime} - ${shift.endTime}):\n`;
      result += `    Lương: ${shift.salary.toLocaleString('vi-VN')} VNĐ/ca\n`;
      result += `    Số người: ${shift.numberOfWorkers}\n`;
      result += `    Số ngày làm việc: ${shift.workingDaysCount}\n`;
      result += `    Thành tiền: ${shift.shiftAmount.toLocaleString('vi-VN')} VNĐ\n\n`;
    });
    
    result += `  Tổng ${job.jobCategoryName}: ${job.jobTotal.toLocaleString('vi-VN')} VNĐ\n\n`;
  });

  return result;
};
