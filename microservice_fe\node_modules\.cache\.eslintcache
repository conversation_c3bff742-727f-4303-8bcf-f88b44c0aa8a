[{"D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\index.tsx": "1", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\reportWebVitals.ts": "2", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\App.tsx": "3", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\index.ts": "4", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\layout\\index.ts": "5", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\HomePage.tsx": "6", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\ContractsListPage.tsx": "7", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\ContractDetailsPage.tsx": "8", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\CreateContractPage.tsx": "9", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\NotFoundPage.tsx": "10", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\layout\\Navbar.tsx": "11", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\layout\\Footer.tsx": "12", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\layout\\Layout.tsx": "13", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\utils\\dateUtils.ts": "14", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\contract\\contractService.ts": "15", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\index.ts": "16", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\index.ts": "17", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\index.ts": "18", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\PageHeader.tsx": "19", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\LoadingSpinner.tsx": "20", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\SuccessAlert.tsx": "21", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\ErrorAlert.tsx": "22", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\ConfirmDialog.tsx": "23", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\api\\apiClient.ts": "24", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\JobCategory.ts": "25", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\WorkShift.ts": "26", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\CustomerContract.ts": "27", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\Customer.ts": "28", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\JobDetail.ts": "29", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\JobDetailForm.tsx": "30", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\CustomerContractForm.tsx": "31", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\ContractDetails.tsx": "32", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\WorkShiftForm.tsx": "33", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\utils\\workingDaysUtils.ts": "34", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\job\\jobCategoryService.ts": "35", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\customer\\customerService.ts": "36", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\utils\\currencyUtils.ts": "37", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\customer\\index.ts": "38", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\customer\\CustomerForm.tsx": "39", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\customer\\CustomerDialog.tsx": "40", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\CustomerPayment.ts": "41", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\CustomerRevenue.ts": "42", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\utils\\formatters.ts": "43", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\CustomerPaymentPage.tsx": "44", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\index.ts": "45", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\payment\\index.ts": "46", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\payment\\customerPaymentService.ts": "47", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\payment\\CustomerSearchForm.tsx": "48", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\payment\\PaymentForm.tsx": "49", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\payment\\CustomerContractList.tsx": "50", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\payment\\CustomerList.tsx": "51", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\payment\\SuccessNotification.tsx": "52", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\CustomerStatisticsPage.tsx": "53", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\statistics\\customerStatisticsService.ts": "54", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\index.ts": "55", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\CustomerRevenueList.tsx": "56", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\CustomerInvoiceList.tsx": "57", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\TimeBasedRevenue.ts": "58", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\TimeBasedStatisticsSelector.tsx": "59", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\TimeBasedRevenueDisplay.tsx": "60", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\DatePickerField.tsx": "61", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\BarChartDisplay.tsx": "62", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\StatisticsSummary.tsx": "63"}, {"size": 868, "mtime": 1747975394144, "results": "64", "hashOfConfig": "65"}, {"size": 425, "mtime": 1747901853368, "results": "66", "hashOfConfig": "65"}, {"size": 1793, "mtime": 1747923851728, "results": "67", "hashOfConfig": "65"}, {"size": 468, "mtime": 1747923851728, "results": "68", "hashOfConfig": "65"}, {"size": 138, "mtime": 1747902225279, "results": "69", "hashOfConfig": "65"}, {"size": 3142, "mtime": 1747910578132, "results": "70", "hashOfConfig": "65"}, {"size": 10051, "mtime": 1747931792216, "results": "71", "hashOfConfig": "65"}, {"size": 2913, "mtime": 1747930391001, "results": "72", "hashOfConfig": "65"}, {"size": 4871, "mtime": 1747998670206, "results": "73", "hashOfConfig": "65"}, {"size": 1078, "mtime": 1747902509716, "results": "74", "hashOfConfig": "65"}, {"size": 1836, "mtime": 1747910540518, "results": "75", "hashOfConfig": "65"}, {"size": 571, "mtime": 1747904248056, "results": "76", "hashOfConfig": "65"}, {"size": 605, "mtime": 1747902220275, "results": "77", "hashOfConfig": "65"}, {"size": 4372, "mtime": 1747932197380, "results": "78", "hashOfConfig": "65"}, {"size": 4503, "mtime": 1747930605055, "results": "79", "hashOfConfig": "65"}, {"size": 352, "mtime": 1747975523634, "results": "80", "hashOfConfig": "65"}, {"size": 258, "mtime": 1747902337151, "results": "81", "hashOfConfig": "65"}, {"size": 259, "mtime": 1747927809976, "results": "82", "hashOfConfig": "65"}, {"size": 577, "mtime": 1747902174150, "results": "83", "hashOfConfig": "65"}, {"size": 992, "mtime": 1747902149527, "results": "84", "hashOfConfig": "65"}, {"size": 846, "mtime": 1747902165347, "results": "85", "hashOfConfig": "65"}, {"size": 2240, "mtime": 1747926050567, "results": "86", "hashOfConfig": "65"}, {"size": 1316, "mtime": 1747906323357, "results": "87", "hashOfConfig": "65"}, {"size": 7044, "mtime": 1747927004866, "results": "88", "hashOfConfig": "65"}, {"size": 155, "mtime": 1747901999771, "results": "89", "hashOfConfig": "65"}, {"size": 292, "mtime": 1747998489731, "results": "90", "hashOfConfig": "65"}, {"size": 624, "mtime": 1747976181168, "results": "91", "hashOfConfig": "65"}, {"size": 252, "mtime": 1747922140447, "results": "92", "hashOfConfig": "65"}, {"size": 346, "mtime": 1747902012555, "results": "93", "hashOfConfig": "65"}, {"size": 9760, "mtime": 1747998591179, "results": "94", "hashOfConfig": "65"}, {"size": 11950, "mtime": 1747975565001, "results": "95", "hashOfConfig": "65"}, {"size": 17807, "mtime": 1747998658373, "results": "96", "hashOfConfig": "65"}, {"size": 7624, "mtime": 1748230426622, "results": "97", "hashOfConfig": "65"}, {"size": 3543, "mtime": 1747998520066, "results": "98", "hashOfConfig": "65"}, {"size": 948, "mtime": 1747902060729, "results": "99", "hashOfConfig": "65"}, {"size": 1363, "mtime": 1747907859946, "results": "100", "hashOfConfig": "65"}, {"size": 790, "mtime": 1747906005248, "results": "101", "hashOfConfig": "65"}, {"size": 120, "mtime": 1747907779789, "results": "102", "hashOfConfig": "65"}, {"size": 5514, "mtime": 1747908527895, "results": "103", "hashOfConfig": "65"}, {"size": 12344, "mtime": 1747987012484, "results": "104", "hashOfConfig": "65"}, {"size": 415, "mtime": 1747930255221, "results": "105", "hashOfConfig": "65"}, {"size": 4238, "mtime": 1747931208285, "results": "106", "hashOfConfig": "65"}, {"size": 1351, "mtime": 1747932215756, "results": "107", "hashOfConfig": "65"}, {"size": 8628, "mtime": 1747986997063, "results": "108", "hashOfConfig": "65"}, {"size": 213, "mtime": 1747922856213, "results": "109", "hashOfConfig": "65"}, {"size": 330, "mtime": 1747911990280, "results": "110", "hashOfConfig": "65"}, {"size": 2494, "mtime": 1747986981447, "results": "111", "hashOfConfig": "65"}, {"size": 3892, "mtime": 1747910324864, "results": "112", "hashOfConfig": "65"}, {"size": 9519, "mtime": 1747976446884, "results": "113", "hashOfConfig": "65"}, {"size": 6737, "mtime": 1747976502128, "results": "114", "hashOfConfig": "65"}, {"size": 6283, "mtime": 1747911777952, "results": "115", "hashOfConfig": "65"}, {"size": 2257, "mtime": 1747912064565, "results": "116", "hashOfConfig": "65"}, {"size": 28838, "mtime": 1747994625869, "results": "117", "hashOfConfig": "65"}, {"size": 24610, "mtime": 1747933042688, "results": "118", "hashOfConfig": "65"}, {"size": 368, "mtime": 1747928054242, "results": "119", "hashOfConfig": "65"}, {"size": 2838, "mtime": 1747921471664, "results": "120", "hashOfConfig": "65"}, {"size": 3188, "mtime": 1747932337817, "results": "121", "hashOfConfig": "65"}, {"size": 1849, "mtime": 1747994411130, "results": "122", "hashOfConfig": "65"}, {"size": 6202, "mtime": 1747995320916, "results": "123", "hashOfConfig": "65"}, {"size": 7312, "mtime": 1747994456699, "results": "124", "hashOfConfig": "65"}, {"size": 2066, "mtime": 1747975512424, "results": "125", "hashOfConfig": "65"}, {"size": 3267, "mtime": 1747978131060, "results": "126", "hashOfConfig": "65"}, {"size": 2607, "mtime": 1747994496465, "results": "127", "hashOfConfig": "65"}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "10nwx99", {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\index.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\reportWebVitals.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\App.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\index.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\layout\\index.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\HomePage.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\ContractsListPage.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\ContractDetailsPage.tsx", ["317"], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\CreateContractPage.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\NotFoundPage.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\layout\\Navbar.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\layout\\Footer.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\layout\\Layout.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\utils\\dateUtils.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\contract\\contractService.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\index.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\index.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\index.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\PageHeader.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\LoadingSpinner.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\SuccessAlert.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\ErrorAlert.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\ConfirmDialog.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\api\\apiClient.ts", ["318"], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\JobCategory.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\WorkShift.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\CustomerContract.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\Customer.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\JobDetail.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\JobDetailForm.tsx", ["319", "320", "321", "322"], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\CustomerContractForm.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\ContractDetails.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\WorkShiftForm.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\utils\\workingDaysUtils.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\job\\jobCategoryService.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\customer\\customerService.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\utils\\currencyUtils.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\customer\\index.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\customer\\CustomerForm.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\customer\\CustomerDialog.tsx", ["323", "324", "325", "326", "327", "328", "329", "330", "331"], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\CustomerPayment.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\CustomerRevenue.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\utils\\formatters.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\CustomerPaymentPage.tsx", ["332"], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\index.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\payment\\index.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\payment\\customerPaymentService.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\payment\\CustomerSearchForm.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\payment\\PaymentForm.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\payment\\CustomerContractList.tsx", ["333"], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\payment\\CustomerList.tsx", ["334", "335", "336"], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\payment\\SuccessNotification.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\CustomerStatisticsPage.tsx", [], ["337"], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\statistics\\customerStatisticsService.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\index.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\CustomerRevenueList.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\CustomerInvoiceList.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\TimeBasedRevenue.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\TimeBasedStatisticsSelector.tsx", ["338", "339", "340", "341"], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\TimeBasedRevenueDisplay.tsx", ["342", "343", "344", "345"], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\DatePickerField.tsx", ["346"], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\BarChartDisplay.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\StatisticsSummary.tsx", [], [], {"ruleId": "347", "severity": 1, "message": "348", "line": 3, "column": 23, "nodeType": "349", "messageId": "350", "endLine": 3, "endColumn": 33}, {"ruleId": "347", "severity": 1, "message": "351", "line": 18, "column": 7, "nodeType": "349", "messageId": "350", "endLine": 18, "endColumn": 22}, {"ruleId": "347", "severity": 1, "message": "352", "line": 16, "column": 3, "nodeType": "349", "messageId": "350", "endLine": 16, "endColumn": 7}, {"ruleId": "347", "severity": 1, "message": "353", "line": 17, "column": 3, "nodeType": "349", "messageId": "350", "endLine": 17, "endColumn": 7}, {"ruleId": "347", "severity": 1, "message": "354", "line": 30, "column": 10, "nodeType": "349", "messageId": "350", "endLine": 30, "endColumn": 28}, {"ruleId": "347", "severity": 1, "message": "355", "line": 30, "column": 30, "nodeType": "349", "messageId": "350", "endLine": 30, "endColumn": 49}, {"ruleId": "347", "severity": 1, "message": "356", "line": 17, "column": 3, "nodeType": "349", "messageId": "350", "endLine": 17, "endColumn": 13}, {"ruleId": "347", "severity": 1, "message": "353", "line": 21, "column": 3, "nodeType": "349", "messageId": "350", "endLine": 21, "endColumn": 7}, {"ruleId": "347", "severity": 1, "message": "357", "line": 22, "column": 3, "nodeType": "349", "messageId": "350", "endLine": 22, "endColumn": 10}, {"ruleId": "347", "severity": 1, "message": "358", "line": 23, "column": 3, "nodeType": "349", "messageId": "350", "endLine": 23, "endColumn": 7}, {"ruleId": "347", "severity": 1, "message": "359", "line": 24, "column": 3, "nodeType": "349", "messageId": "350", "endLine": 24, "endColumn": 14}, {"ruleId": "347", "severity": 1, "message": "360", "line": 29, "column": 8, "nodeType": "349", "messageId": "350", "endLine": 29, "endColumn": 18}, {"ruleId": "347", "severity": 1, "message": "361", "line": 30, "column": 8, "nodeType": "349", "messageId": "350", "endLine": 30, "endColumn": 20}, {"ruleId": "347", "severity": 1, "message": "355", "line": 38, "column": 21, "nodeType": "349", "messageId": "350", "endLine": 38, "endColumn": 40}, {"ruleId": "347", "severity": 1, "message": "362", "line": 53, "column": 10, "nodeType": "349", "messageId": "350", "endLine": 53, "endColumn": 26}, {"ruleId": "347", "severity": 1, "message": "363", "line": 15, "column": 8, "nodeType": "349", "messageId": "350", "endLine": 15, "endColumn": 18}, {"ruleId": "347", "severity": 1, "message": "352", "line": 17, "column": 3, "nodeType": "349", "messageId": "350", "endLine": 17, "endColumn": 7}, {"ruleId": "347", "severity": 1, "message": "356", "line": 7, "column": 3, "nodeType": "349", "messageId": "350", "endLine": 7, "endColumn": 13}, {"ruleId": "347", "severity": 1, "message": "357", "line": 22, "column": 3, "nodeType": "349", "messageId": "350", "endLine": 22, "endColumn": 10}, {"ruleId": "347", "severity": 1, "message": "360", "line": 25, "column": 8, "nodeType": "349", "messageId": "350", "endLine": 25, "endColumn": 18}, {"ruleId": "364", "severity": 1, "message": "365", "line": 592, "column": 6, "nodeType": "366", "endLine": 592, "endColumn": 8, "suggestions": "367", "suppressions": "368"}, {"ruleId": "347", "severity": 1, "message": "369", "line": 1, "column": 17, "nodeType": "349", "messageId": "350", "endLine": 1, "endColumn": 25}, {"ruleId": "347", "severity": 1, "message": "370", "line": 9, "column": 3, "nodeType": "349", "messageId": "350", "endLine": 9, "endColumn": 9}, {"ruleId": "347", "severity": 1, "message": "371", "line": 15, "column": 3, "nodeType": "349", "messageId": "350", "endLine": 15, "endColumn": 12}, {"ruleId": "347", "severity": 1, "message": "372", "line": 17, "column": 10, "nodeType": "349", "messageId": "350", "endLine": 17, "endColumn": 30}, {"ruleId": "347", "severity": 1, "message": "358", "line": 12, "column": 3, "nodeType": "349", "messageId": "350", "endLine": 12, "endColumn": 7}, {"ruleId": "347", "severity": 1, "message": "359", "line": 13, "column": 3, "nodeType": "349", "messageId": "350", "endLine": 13, "endColumn": 14}, {"ruleId": "347", "severity": 1, "message": "352", "line": 14, "column": 3, "nodeType": "349", "messageId": "350", "endLine": 14, "endColumn": 7}, {"ruleId": "347", "severity": 1, "message": "357", "line": 15, "column": 3, "nodeType": "349", "messageId": "350", "endLine": 15, "endColumn": 10}, {"ruleId": "347", "severity": 1, "message": "373", "line": 3, "column": 10, "nodeType": "349", "messageId": "350", "endLine": 3, "endColumn": 19}, "@typescript-eslint/no-unused-vars", "'Typography' is defined but never used.", "Identifier", "unusedVar", "'checkApiGateway' is assigned a value but never used.", "'Grid' is defined but never used.", "'Chip' is defined but never used.", "'formatDateForInput' is defined but never used.", "'formatDateLocalized' is defined but never used.", "'IconButton' is defined but never used.", "'Divider' is defined but never used.", "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'PersonIcon' is defined but never used.", "'BusinessIcon' is defined but never used.", "'selectedCustomer' is assigned a value but never used.", "'SearchIcon' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadCustomerStatistics'. Either include it or remove the dependency array.", "ArrayExpression", ["374"], ["375"], "'useState' is defined but never used.", "'Button' is defined but never used.", "'FormLabel' is defined but never used.", "'formatDateForDisplay' is defined but never used.", "'TextField' is defined but never used.", {"desc": "376", "fix": "377"}, {"kind": "378", "justification": "379"}, "Update the dependencies array to be: [loadCustomerStatistics]", {"range": "380", "text": "381"}, "directive", "", [22117, 22119], "[loadCustomerStatistics]"]